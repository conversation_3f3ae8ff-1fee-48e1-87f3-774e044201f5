#%%
# 船舶冲突真实可视化 - 固定显示范围版本
# 将此代码块添加到51.complex_network.ipynb的最后
# 取消缩放，直接使用base_map_extent显示范围

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.patches import Ellipse, Polygon as MplPolygon
import pickle
import os

def draw_ship_symbol(ax, lon, lat, cog, color='red', size=50):
    """绘制船舶符号"""
    angle_rad = np.radians(90 - cog)
    ship_length = size * 1e-5
    ship_width = ship_length * 0.4
    
    bow_x = lon + ship_length * np.cos(angle_rad)
    bow_y = lat + ship_length * np.sin(angle_rad)
    
    stern_left_angle = angle_rad + np.pi * 2/3
    stern_left_x = lon + ship_width * np.cos(stern_left_angle)
    stern_left_y = lat + ship_width * np.sin(stern_left_angle)
    
    stern_right_angle = angle_rad - np.pi * 2/3
    stern_right_x = lon + ship_width * np.cos(stern_right_angle)
    stern_right_y = lat + ship_width * np.sin(stern_right_angle)
    
    triangle = MplPolygon([(bow_x, bow_y), (stern_left_x, stern_left_y), 
                          (stern_right_x, stern_right_y)], 
                         facecolor=color, edgecolor='black', linewidth=1, alpha=0.8)
    ax.add_patch(triangle)

def draw_ship_domain(ax, lon, lat, cog, ship_type, ship_length, color='red', alpha=0.3):
    """绘制船舶领域椭圆"""
    # 使用与51.complex_network.ipynb中vectorized_get_ab相同的逻辑
    if ship_type == 1:  # 穿越船
        if ship_length <= 100:
            a_meters, b_meters = 271, 192
        else:
            a_meters, b_meters = 375, 210
    else:  # 非穿越船
        if ship_length <= 100:
            a_meters, b_meters = 180, 85
        else:
            a_meters, b_meters = 290, 120

    lat_rad = np.radians(lat)
    meters_per_degree_lon = 111320 * np.cos(lat_rad)
    meters_per_degree_lat = 111320

    a_degrees = a_meters / meters_per_degree_lon
    b_degrees = b_meters / meters_per_degree_lat
    angle = 90 - cog

    ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, 
                     angle=angle, facecolor=color, alpha=alpha, 
                     edgecolor=color, linewidth=1.5)
    ax.add_patch(ellipse)

def draw_channel_boundaries(ax):
    """绘制航道边界线"""
    try:
        # 使用已经加载的geo变量
        if 'channel_side1' in geo:
            side1_data = geo['channel_side1']
            if side1_data and len(side1_data) > 0:
                side1_lons = [p[0] for p in side1_data]
                side1_lats = [p[1] for p in side1_data]
                ax.plot(side1_lons, side1_lats, 'green', linewidth=2, alpha=0.8, linestyle='--')

        if 'channel_side2' in geo:
            side2_data = geo['channel_side2']
            if side2_data and len(side2_data) > 0:
                side2_lons = [p[0] for p in side2_data]
                side2_lats = [p[1] for p in side2_data]
                ax.plot(side2_lons, side2_lats, 'green', linewidth=2, alpha=0.8, linestyle='--')

        if 'channel_centerline' in geo:
            centerline_data = geo['channel_centerline']
            if centerline_data and len(centerline_data) > 0:
                center_lons = [p[0] for p in centerline_data]
                center_lats = [p[1] for p in centerline_data]
                ax.plot(center_lons, center_lats, 'blue', linewidth=1.5, alpha=0.6, linestyle='-')
    except Exception as e:
        pass

def visualize_conflict_ships_fixed_extent(postime=None, conflict_threshold=0.01):
    """
    固定显示范围的冲突船舶可视化函数
    直接使用base_map_extent，不进行缩放
    """
    
    # 如果没有指定时刻，选择冲突最多的时刻
    if postime is None:
        conflict_data = conflicts_part_df[conflicts_part_df['conflict'] > conflict_threshold]
        if len(conflict_data) == 0:
            print("没有找到满足阈值的冲突数据")
            return
        
        conflict_counts = conflict_data.groupby('PosTime').size()
        postime = conflict_counts.idxmax()
        print(f"自动选择冲突最多的时刻: {postime} (共 {conflict_counts[postime]} 个冲突)")
    
    # 获取该时刻的冲突数据
    moment_conflicts = conflicts_part_df[
        (conflicts_part_df['PosTime'] == postime) & 
        (conflicts_part_df['conflict'] > conflict_threshold)
    ]
    
    if len(moment_conflicts) == 0:
        print(f"时刻 {postime} 没有冲突数据")
        return
    
    # 获取涉及冲突的船舶ID
    conflict_ship_ids = set()
    for _, conflict in moment_conflicts.iterrows():
        id1, id2 = conflict['id_pair']
        conflict_ship_ids.add(id1)
        conflict_ship_ids.add(id2)
    
    # 获取该时刻涉及冲突的船舶数据
    moment_ships = trajectory_note_df[
        (trajectory_note_df['PosTime'] == postime) & 
        (trajectory_note_df['ID'].isin(conflict_ship_ids))
    ].copy()
    
    if len(moment_ships) == 0:
        print(f"时刻 {postime} 没有找到对应的船舶数据")
        return
    
    # 固定使用base_map_extent，不进行任何缩放计算
    base_map_extent = [121.050, 121.350, 31.516, 31.784]
    display_extent = base_map_extent
    
    # 固定图片尺寸（基于base_map_extent的比例）
    lon_range = display_extent[1] - display_extent[0]  # 0.3度
    lat_range = display_extent[3] - display_extent[2]  # 0.268度
    
    # 计算地理比例
    center_lat = (display_extent[2] + display_extent[3]) / 2
    lat_km_per_degree = 111.0
    lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))
    
    actual_lon_distance = lon_range * lon_km_per_degree
    actual_lat_distance = lat_range * lat_km_per_degree
    geographic_aspect_ratio = actual_lat_distance / actual_lon_distance
    
    # 固定宽度，按比例计算高度
    base_width = 12
    height = base_width * geographic_aspect_ratio
    figsize = (base_width, height)
    
    print(f"显示范围: 经度 {display_extent[0]:.3f} - {display_extent[1]:.3f}, 纬度 {display_extent[2]:.3f} - {display_extent[3]:.3f}")
    print(f"图片尺寸: {figsize[0]:.1f} x {figsize[1]:.1f}")
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=figsize)
    
    # 设置底图
    map_path = 'data/map0.png'
    if os.path.exists(map_path):
        try:
            base_map = mpimg.imread(map_path)
            ax.imshow(base_map, extent=base_map_extent, aspect='auto', alpha=0.7)
            print("✅ 底图加载成功")
        except Exception as e:
            print(f"⚠️ 底图加载失败: {e}")
    else:
        print("⚠️ 底图文件不存在")
    
    # 设置固定显示范围
    ax.set_xlim(display_extent[0], display_extent[1])
    ax.set_ylim(display_extent[2], display_extent[3])
    
    # 绘制航道边界
    draw_channel_boundaries(ax)
    
    # 绘制船舶和领域
    ship_count = 0
    for _, ship in moment_ships.iterrows():
        # 检查船舶是否在显示范围内
        if (display_extent[0] <= ship['Lon'] <= display_extent[1] and 
            display_extent[2] <= ship['Lat'] <= display_extent[3]):
            
            color = 'red'  # 所有冲突船舶都用红色
            
            # 绘制船舶领域
            draw_ship_domain(ax, ship['Lon'], ship['Lat'], ship['Cog'], 
                            ship['Type'], ship['Length'], color, alpha=0.4)
            
            # 绘制船舶符号
            draw_ship_symbol(ax, ship['Lon'], ship['Lat'], ship['Cog'], 
                            color, size=max(50, ship['Length']))
            ship_count += 1
    
    # 绘制冲突连线
    conflict_lines = 0
    for _, conflict in moment_conflicts.iterrows():
        id1, id2 = conflict['id_pair']
        ship1 = moment_ships[moment_ships['ID'] == id1]
        ship2 = moment_ships[moment_ships['ID'] == id2]
        
        if len(ship1) > 0 and len(ship2) > 0:
            ship1 = ship1.iloc[0]
            ship2 = ship2.iloc[0]
            
            # 检查两艘船是否都在显示范围内
            if (display_extent[0] <= ship1['Lon'] <= display_extent[1] and 
                display_extent[2] <= ship1['Lat'] <= display_extent[3] and
                display_extent[0] <= ship2['Lon'] <= display_extent[1] and 
                display_extent[2] <= ship2['Lat'] <= display_extent[3]):
                
                ax.plot([ship1['Lon'], ship2['Lon']], [ship1['Lat'], ship2['Lat']], 
                       'r--', linewidth=2, alpha=0.8)
                conflict_lines += 1
    
    # 设置标题和标签
    ax.set_title(f'船舶冲突可视化 - 时刻: {postime}\n显示范围内: {ship_count} 艘船, {conflict_lines} 个冲突', 
                fontsize=14, fontweight='bold', pad=20)
    ax.set_xlabel('经度', fontsize=12, fontweight='bold')
    ax.set_ylabel('纬度', fontsize=12, fontweight='bold')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print(f"\n冲突详情 (显示范围内):")
    displayed_conflicts = 0
    for _, conflict in moment_conflicts.iterrows():
        id1, id2 = conflict['id_pair']
        ship1 = moment_ships[moment_ships['ID'] == id1]
        ship2 = moment_ships[moment_ships['ID'] == id2]
        
        if len(ship1) > 0 and len(ship2) > 0:
            ship1 = ship1.iloc[0]
            ship2 = ship2.iloc[0]
            
            # 只显示在范围内的冲突
            if (display_extent[0] <= ship1['Lon'] <= display_extent[1] and 
                display_extent[2] <= ship1['Lat'] <= display_extent[3] and
                display_extent[0] <= ship2['Lon'] <= display_extent[1] and 
                display_extent[2] <= ship2['Lat'] <= display_extent[3]):
                
                print(f"  - 船舶 {conflict['id_pair'][0]} 与 {conflict['id_pair'][1]}: 冲突值 {conflict['conflict']:.3f}")
                displayed_conflicts += 1
    
    if displayed_conflicts == 0:
        print("  (显示范围内没有冲突)")

# 执行可视化
print("🚢 开始船舶冲突可视化 (固定显示范围)...")
print("=" * 60)

# 1. 自动选择冲突最多的时刻，使用固定显示范围
print("\n=== 冲突最严重的时刻 (固定显示范围) ===")
visualize_conflict_ships_fixed_extent()

# 2. 显示冲突统计信息
print("\n=== 冲突数据统计 ===")
conflict_stats = conflicts_part_df[conflicts_part_df['conflict'] > 0.01]
print(f"总冲突记录数: {len(conflict_stats)}")
print(f"涉及的时刻数: {conflict_stats['PosTime'].nunique()}")
if len(conflict_stats) > 0:
    print(f"冲突值范围: {conflict_stats['conflict'].min():.3f} - {conflict_stats['conflict'].max():.3f}")
    
    # 3. 显示冲突最多的前3个时刻
    conflict_counts = conflict_stats.groupby('PosTime').size().sort_values(ascending=False)
    top_times = conflict_counts.head(3)
    print(f"\n冲突最多的时刻:")
    for postime, count in top_times.items():
        print(f"  时刻 {postime}: {count} 个冲突")

print("\n✅ 船舶冲突可视化完成！")
print("📍 显示范围固定为: 经度 121.050-121.350, 纬度 31.516-31.784")
