#%%
# 加载数据
import pandas as pd
import pickle
import numpy as np
from shapely.geometry import Polygon, LineString, Point
from tqdm import tqdm
from methods.trans import Trans

trans = Trans()


class CrossIdentify:
    def __init__(self, trajectory_list, geo_information):
        self.trajectory_list = trajectory_list
        self.channel_side1 = geo_information['channel_side1']
        self.channel_side2 = geo_information['channel_side2']
        self.anchorage = geo_information['anchorage']
        self.channel_polygon = geo_information['channel_boundary']

    def cross_owns_indentify(self):
        own_ship_list = []
        for k, tra in enumerate(tqdm(self.trajectory_list)):
            track = [(Lon, lat) for Lon, lat in zip(tra['Lon'], tra['Lat'])]
            if len(track) < 2:
                continue  # 跳过点数不足2的轨迹
            # if (self.is_channel_truly_crossed(track, self.channel_side1, self.channel_side2, consider_direction=True)
            #         & self.is_in_area(track, self.anchorage)):
            if self.is_channel_truly_crossed(track, self.channel_side1, self.channel_side2,
                                             consider_direction=False):
                own_ship_list.append(k)
        return own_ship_list

    def is_channel_truly_crossed(self, track_points, channel_side1, channel_side2, consider_direction=True):
        """判断轨迹是否先穿过side1再穿过side2"""
        track_line = LineString(track_points)
        cross1 = self.find_cross_points(track_line, channel_side1)
        cross2 = self.find_cross_points(track_line, channel_side2)

        # 必须两条线都有交点
        if not cross1 or not cross2:
            return False

        # 找出所有交点对应的轨迹索引
        idx1_list = []
        for pt in cross1:
            min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
            idx1_list.append(min_idx)

        idx2_list = []
        for pt in cross2:
            min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
            idx2_list.append(min_idx)

        # 只考虑有方向的情况
        if consider_direction:
            # 找到最早的side1交点
            first_side1_idx = min(idx1_list)
            # 检查是否存在在这个side1交点之后的side2交点
            later_side2_indices = [idx for idx in idx2_list if idx > first_side1_idx]
            return len(later_side2_indices) > 0
        else:
            # 如果不考虑方向，只要两条线都穿过就行
            return True

    @staticmethod
    def find_cross_points(track_line, side_edges):
        """返回轨迹线与一组边的所有交点"""
        boundary_line = LineString(side_edges)

        # 直接计算交点
        intersection = track_line.intersection(boundary_line)

        # 处理交点
        cross_points = []
        if not intersection.is_empty:
            if intersection.geom_type == 'Point':
                cross_points.append(intersection)
            elif intersection.geom_type == 'MultiPoint':
                cross_points.extend(list(intersection.geoms))

        return cross_points

    @staticmethod
    def is_in_area(track_points, polygon_points):
        poly = Polygon(polygon_points)
        pt = track_points[0]
        if poly.contains(Point(pt)):
            return True
        else:
            return False

    @staticmethod
    def all_in_area(track_points, polygon_points):
        poly = Polygon(polygon_points)
        for pt in track_points:
            if not poly.contains(Point(pt)):
                return False
        return True


def vectorized_get_ab(types, lengths):
    a_vals = np.where(
        types == 1,
        np.where(lengths <= 100, 271, 375),
        np.where(lengths <= 100, 180, 290)
    )
    b_vals = np.where(
        types == 1,
        np.where(lengths <= 100, 192, 210),
        np.where(lengths <= 100, 85, 120)
    )
    return a_vals, b_vals


geo = pickle.load(open('data/geo_info.pkl', 'rb'))
trajectory_list = pickle.load(open('data/2024_1_inter.pkl', 'rb'))

crossIdentify = CrossIdentify(trajectory_list, geo)
cross_ship_idxs = crossIdentify.cross_owns_indentify()

trajectory_note_list = []
for i, tra in enumerate(tqdm(trajectory_list)):
    if i in cross_ship_idxs:
        tra['Type'] = 1
    else:
        tra['Type'] = 0
    trajectory_note_list.append(tra)
trajectory_note_df = pd.concat(trajectory_note_list)

unique_mmsi = trajectory_note_df['MMSI'].unique()
mmsi_to_id = {mmsi: idx + 1 for idx, mmsi in enumerate(unique_mmsi)}
trajectory_note_df['ID'] = trajectory_note_df['MMSI'].map(mmsi_to_id)

trajectory_note_df['A'], trajectory_note_df['B'] = vectorized_get_ab(
    trajectory_note_df['Type'], trajectory_note_df['Length'])
trajectory_note_df['X'], trajectory_note_df['Y'] = trans.LonLat2Gauss(
    trajectory_note_df['Lon'].values, trajectory_note_df['Lat'].values)
trajectory_note_df.to_parquet('data/2024_1_inter_note.parquet')
#%%
# 冲突计算
#%%
import pandas as pd

trajectory_note_df = pd.read_parquet('data/2024_1_inter_note.parquet')
trajectory_note_df.head()
#%%

import pandas as pd
import numpy as np
from shapely.geometry import Point
from shapely.affinity import scale, rotate, translate
from tqdm import tqdm
from methods.trans import Trans

trans = Trans()
geo = pickle.load(open('data/geo_info.pkl', 'rb'))


def overlap_ratios_shapely(m1, n1, A1, B1, phi1,
                           m2, n2, A2, B2, phi2,
                           circle_resolution=256):
    """
    计算两椭圆交集面积分别占各自面积的比值，使用 Shapely。

    参数:
      - m1,n1,A1,B1,phi1: 椭圆1 的中心、长短半轴及旋转角（度）
      - m2,n2,A2,B2,phi2: 椭圆2 同上
      - circle_resolution: 用于近似圆的分段数，越大结果越精确

    返回:
      (ratio1, ratio2)，其中
        ratio1 = area(intersection) / (π A1 B1)
        ratio2 = area(intersection) / (π A2 B2)
    """
    # 构造单位圆
    unit_circle = Point(0, 0).buffer(1, resolution=circle_resolution)

    # 椭圆1：先缩放，再旋转，最后平移
    e1 = scale(unit_circle, A1, B1)
    e1 = rotate(e1, phi1, origin=(0, 0), use_radians=False)
    e1 = translate(e1, m1, n1)

    # 椭圆2
    e2 = scale(unit_circle, A2, B2)
    e2 = rotate(e2, phi2, origin=(0, 0), use_radians=False)
    e2 = translate(e2, m2, n2)

    # 求交集
    inter = e1.intersection(e2)
    area_inter = inter.area

    # 各自面积
    area1 = np.pi * A1 * B1
    area2 = np.pi * A2 * B2

    return max(area_inter / area1, area_inter / area2)


def _get_ship_subregion(position, subregions):
    """判断船舶位置属于哪个子区域"""
    from shapely.geometry import Point, Polygon

    lon, lat = position
    point = Point(lon, lat)

    for subregion_name, subregion_coords in subregions.items():
        # 将坐标列表转换为Shapely多边形
        if isinstance(subregion_coords, list) and len(subregion_coords) >= 3:
            try:
                subregion_polygon = Polygon(subregion_coords)
                if point.within(subregion_polygon):
                    return subregion_name
            except Exception:
                # 如果创建多边形失败，跳过这个子区域
                continue

    return None


def conflict_calculation(df, segment_duration=300):  # 默认5分钟(300秒)
    all_conflicts = []

    # 获取时间范围
    min_time = int(df['PosTime'].min())
    max_time = int(df['PosTime'].max())

    # 随机选择起始时间，确保能容纳完整时间段
    if max_time - min_time < segment_duration:
        start_time = min_time
        end_time = max_time
    else:
        start_time = int(np.random.uniform(min_time, max_time - segment_duration))
        end_time = start_time + segment_duration

    print(f"随机选择时间段: {start_time} - {end_time} (持续{segment_duration}秒)")

    # 筛选时间段内的数据
    segment_df = df[(df['PosTime'] >= start_time) & (df['PosTime'] <= end_time)]

    # 按时间分组处理
    grouped = segment_df.groupby('PosTime')

    for postime, dff in tqdm(grouped, desc="Processing selected time segment"):
        ship_data = dff[['MMSI', 'X', 'Y', 'A', 'B', 'Cog', 'ID', 'Type']].values
        n_ships = len(ship_data)

        # 计算所有船舶对的冲突
        for i in range(n_ships):
            for j in range(i + 1, n_ships):
                ship1, ship2 = ship_data[i], ship_data[j]

                conflict = overlap_ratios_shapely(
                    ship1[1], ship1[2], ship1[3], ship1[4], ship1[5],  # X,Y,A,B,Cog
                    ship2[1], ship2[2], ship2[3], ship2[4], ship2[5]
                )

                # 如果两船都是Type=0（非穿越船），需要检查子航道
                if ship1[7] == 0 and ship2[7] == 0:  # 假设Type在索引7
                    # 获取子区域信息
                    subregions = {}
                    for k in range(1, 5):
                        subregion_key = f'subregion_{k}'
                        if subregion_key in geo:
                            subregions[subregion_key] = geo[subregion_key]

                    if subregions:
                        # 判断两船的子区域
                        ship1_subregion = _get_ship_subregion((ship1[1], ship1[2]), subregions)
                        ship2_subregion = _get_ship_subregion((ship2[1], ship2[2]), subregions)

                        # 如果两船都属于某个子区域但不是同一个，冲突设为0
                        if (ship1_subregion is not None and ship2_subregion is not None
                                and ship1_subregion != ship2_subregion):
                            conflict = 0

                all_conflicts.append({
                    'PosTime': postime,
                    'conflict': conflict,
                    'id_pair': (int(ship1[6]), int(ship2[6])),  # ID
                    'mmsi_pair': (int(ship1[0]), int(ship2[0]))  # MMSI
                })

    print(f"处理了 {len(grouped)} 个时间点，生成 {len(all_conflicts)} 条冲突记录")
    return pd.DataFrame(all_conflicts)


conflicts_part_df = conflict_calculation(trajectory_note_df)
conflicts_part_df.head()
#%%
import networkx as nx
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.pyplot import cm
from itertools import chain
from matplotlib.patches import Rectangle

plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'bold'  # 设置默认字体加粗


def vis(nx_graph):
    fig, ax = plt.subplots(figsize=(8, 5))
    seed = 4
    # 优化布局参数
    pos = nx.forceatlas2_layout(nx_graph, gravity=8, seed=seed)  # Scale the layout

    # Calculate edge weights and colors
    weights = [nx_graph[u][v]['weight'] for u, v in nx_graph.edges()]
    edge_colors = weights
    camp = cm.YlOrRd
    # Draw the graph
    # plt.subplot(121)
    nx.draw(nx_graph, pos,
            with_labels=True,
            node_color='lightblue',
            edge_color=edge_colors,
            edge_cmap=camp,
            width=[2 for w in weights],  # Scale edge widths
            node_size=200,  # Increase node size for better visibility
            font_size=8,
            font_weight='bold',
            font_family='Times New Roman')

    # Add edge labels
    # edge_labels = nx.get_edge_attributes(nx_graph, 'weight')
    # Uncomment to display edge weights
    # nx.draw_networkx_edge_labels(nx_graph, pos,
    #                              edge_labels=edge_labels,
    #                              font_size=8)

    # plt.title(title, pad=20)
    # plt.axis('off')
    # Add color bar for edge weights
    sm = plt.cm.ScalarMappable(cmap=camp,
                               norm=plt.Normalize(vmin=0, vmax=1))
    sm.set_array([])  # Necessary for the colorbar
    # plt.colorbar(sm, ax=ax, shrink=0.8, label="Edge Weight", aspect=25)  # aspect越大-越细
    plt.colorbar(sm, ax=ax, shrink=1, aspect=25)  # aspect越大-越细

    # —— 在这里添加一个无填充的 Rectangle 作为边框 —— #
    rect = Rectangle((0, 0), 1, 1,
                     transform=ax.transAxes,  # 以 Axes 归一化坐标为单位
                     linewidth=1,
                     edgecolor='black',
                     facecolor='none',
                     zorder=10)
    ax.add_patch(rect)

    plt.tight_layout(pad=2)
    # plt.savefig(f'vis/network/{title}.svg', dpi=600)

    plt.show()
    plt.close()


# 获取所有时刻并为每个时刻创建和可视化图
unique_times = conflicts_part_df['PosTime'].unique()
print(f"总共有 {len(unique_times)} 个时刻需要可视化")

for i, postime in enumerate(unique_times):
    print(f"处理第 {i+1}/{len(unique_times)} 个时刻: {postime}")

    # 筛选当前时刻的冲突数据
    conflicts_part_df1 = conflicts_part_df[conflicts_part_df['PosTime'] == postime]
    conflicts_part_df1 = conflicts_part_df1[conflicts_part_df1['conflict'] > 0]

    # 如果当前时刻没有冲突，跳过
    if len(conflicts_part_df1) == 0:
        print(f"时刻 {postime} 没有冲突，跳过")
        continue

    ship_pairs = np.array([list(pair) for pair in conflicts_part_df1['id_pair']])
    conflict_weights = conflicts_part_df1['conflict'].values

    G = nx.Graph()
    # 添加所有唯一的节点
    all_nodes = np.unique(ship_pairs.flatten())
    G.add_nodes_from(all_nodes)

    # 添加边和权重
    edges = [(int(a), int(b), {'weight': float(c)})
             for a, b, c in zip(ship_pairs[:, 0], ship_pairs[:, 1], conflict_weights)]
    G.add_edges_from(edges)

    # 可视化当前时刻的图
    print(f"时刻 {postime}: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边")
    vis(G)
#%%
