import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.patches import Ellipse, Polygon as MplPolygon
from matplotlib.collections import LineCollection
import pickle
import os
from methods.trans import Trans

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.weight'] = 'bold'


class ShipDomainRealisticVisualizer:
    """
    真实船舶位置和船舶领域可视化器
    """

    def __init__(self):
        """初始化可视化器"""
        self.base_map = None
        self.base_map_extent = [121.050, 121.350, 31.516, 31.784]
        self.trans = Trans()
        self._load_base_map()
        self._load_geo_info()

    def _load_base_map(self):
        """加载底图"""
        map_path = 'data/map0.png'
        if os.path.exists(map_path):
            try:
                self.base_map = mpimg.imread(map_path)
            except Exception as e:
                print(f"加载底图失败: {e}")
                self.base_map = None

    def _load_geo_info(self):
        """加载地理信息"""
        try:
            with open('data/geo_info.pkl', 'rb') as f:
                self.geo_info = pickle.load(f)
        except Exception as e:
            print(f"加载地理信息失败: {e}")
            self.geo_info = {}

    def _draw_channel_boundaries(self, ax):
        """绘制航道边界线"""
        try:
            # 绘制航道边界1
            if 'channel_side1' in self.geo_info:
                side1_data = self.geo_info['channel_side1']
                if side1_data and len(side1_data) > 0:
                    side1_lons = [p[0] for p in side1_data]
                    side1_lats = [p[1] for p in side1_data]
                    ax.plot(side1_lons, side1_lats, 'green', linewidth=2, alpha=0.8, 
                           linestyle='--', label='航道边界1')

            # 绘制航道边界2
            if 'channel_side2' in self.geo_info:
                side2_data = self.geo_info['channel_side2']
                if side2_data and len(side2_data) > 0:
                    side2_lons = [p[0] for p in side2_data]
                    side2_lats = [p[1] for p in side2_data]
                    ax.plot(side2_lons, side2_lats, 'green', linewidth=2, alpha=0.8, 
                           linestyle='--', label='航道边界2')

            # 绘制航道中心线
            if 'channel_centerline' in self.geo_info:
                centerline_data = self.geo_info['channel_centerline']
                if centerline_data and len(centerline_data) > 0:
                    center_lons = [p[0] for p in centerline_data]
                    center_lats = [p[1] for p in centerline_data]
                    ax.plot(center_lons, center_lats, 'blue', linewidth=1.5, alpha=0.6, 
                           linestyle='-', label='航道中心线')

        except Exception as e:
            print(f"绘制航道边界失败: {e}")

    def _draw_ship_symbol(self, ax, lon, lat, cog, ship_type, color='red', size=50):
        """绘制船舶符号（三角形箭头指向航向）"""
        # 将航向角转换为数学角度（北向为0度，顺时针为正）
        # matplotlib中角度是逆时针为正，0度为东向
        angle_rad = np.radians(90 - cog)
        
        # 船舶符号的大小（经纬度单位）
        ship_length = size * 1e-5  # 调整船舶符号大小
        ship_width = ship_length * 0.4
        
        # 计算三角形的三个顶点
        # 船头（前端）
        bow_x = lon + ship_length * np.cos(angle_rad)
        bow_y = lat + ship_length * np.sin(angle_rad)
        
        # 船尾左侧
        stern_left_angle = angle_rad + np.pi * 2/3
        stern_left_x = lon + ship_width * np.cos(stern_left_angle)
        stern_left_y = lat + ship_width * np.sin(stern_left_angle)
        
        # 船尾右侧
        stern_right_angle = angle_rad - np.pi * 2/3
        stern_right_x = lon + ship_width * np.cos(stern_right_angle)
        stern_right_y = lat + ship_width * np.sin(stern_right_angle)
        
        # 创建三角形
        triangle = MplPolygon([(bow_x, bow_y), (stern_left_x, stern_left_y), 
                              (stern_right_x, stern_right_y)], 
                             facecolor=color, edgecolor='black', linewidth=1, alpha=0.8)
        ax.add_patch(triangle)
        
        return triangle

    def _draw_ship_domain(self, ax, lon, lat, cog, ship_type, ship_length, color='red', alpha=0.3):
        """绘制船舶领域椭圆"""
        # 根据船舶类型和长度获取椭圆参数
        if ship_type == 1:  # 穿越船
            if ship_length <= 100:
                a_meters, b_meters = 271, 192
            else:
                a_meters, b_meters = 375, 210
        else:  # 非穿越船
            if ship_length <= 100:
                a_meters, b_meters = 180, 85
            else:
                a_meters, b_meters = 290, 120

        # 将米转换为度
        lat_rad = np.radians(lat)
        meters_per_degree_lon = 111320 * np.cos(lat_rad)
        meters_per_degree_lat = 111320

        a_degrees = a_meters / meters_per_degree_lon
        b_degrees = b_meters / meters_per_degree_lat

        # 航向转换（北向为0度转换为数学角度）
        angle = 90 - cog

        # 创建椭圆
        ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, 
                         angle=angle, facecolor=color, alpha=alpha, 
                         edgecolor=color, linewidth=1.5)
        ax.add_patch(ellipse)
        
        return ellipse

    def _calculate_display_extent(self, ship_data, expand_ratio=0.2):
        """计算显示范围"""
        lons = ship_data['Lon'].values
        lats = ship_data['Lat'].values
        
        lon_min, lon_max = lons.min(), lons.max()
        lat_min, lat_max = lats.min(), lats.max()
        
        lon_range = lon_max - lon_min
        lat_range = lat_max - lat_min
        
        # 确保最小显示范围
        if lon_range < 0.01:
            lon_range = 0.01
        if lat_range < 0.01:
            lat_range = 0.01
        
        lon_expand = lon_range * expand_ratio
        lat_expand = lat_range * expand_ratio
        
        display_extent = [
            lon_min - lon_expand, lon_max + lon_expand,
            lat_min - lat_expand, lat_max + lat_expand
        ]
        
        return display_extent

    def _calculate_figure_size(self, display_extent, base_width=12):
        """计算图片尺寸"""
        lon_range = display_extent[1] - display_extent[0]
        lat_range = display_extent[3] - display_extent[2]

        center_lat = (display_extent[2] + display_extent[3]) / 2
        lat_km_per_degree = 111.0
        lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))

        actual_lon_distance = lon_range * lon_km_per_degree
        actual_lat_distance = lat_range * lat_km_per_degree
        geographic_aspect_ratio = actual_lat_distance / actual_lon_distance

        height = base_width * geographic_aspect_ratio
        height = max(6, min(height, 20))

        return (base_width, height)

    def _setup_base_plot(self, ax, display_extent, show_channel=True, show_map=True):
        """设置基础图形"""
        # 设置底图
        if show_map and self.base_map is not None:
            ax.imshow(self.base_map, extent=self.base_map_extent, aspect='auto', alpha=0.7)

        # 设置显示范围
        ax.set_xlim(display_extent[0], display_extent[1])
        ax.set_ylim(display_extent[2], display_extent[3])

        # 绘制航道边界线
        if show_channel:
            self._draw_channel_boundaries(ax)

        ax.set_xlabel('经度 (°)', fontsize=12, fontweight='bold')
        ax.set_ylabel('纬度 (°)', fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)

    def visualize_single_moment(self, trajectory_df, postime, output_dir='vis/ship_domain_realistic', 
                               show_channel=True, show_map=True, show_domain=True, 
                               show_ship_info=True, figsize=None):
        """
        可视化单个时刻的船舶位置和领域
        
        Args:
            trajectory_df: 轨迹数据DataFrame
            postime: 要可视化的时刻
            output_dir: 输出目录
            show_channel: 是否显示航道边界
            show_map: 是否显示底图
            show_domain: 是否显示船舶领域
            show_ship_info: 是否显示船舶信息
            figsize: 图片尺寸，如果为None则自动计算
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 筛选指定时刻的数据
        moment_data = trajectory_df[trajectory_df['PosTime'] == postime].copy()
        
        if len(moment_data) == 0:
            print(f"时刻 {postime} 没有船舶数据")
            return
        
        # 计算显示范围和图片尺寸
        display_extent = self._calculate_display_extent(moment_data)
        if figsize is None:
            figsize = self._calculate_figure_size(display_extent)
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=figsize)
        
        # 设置基础图形
        self._setup_base_plot(ax, display_extent, show_channel, show_map)
        
        # 定义颜色映射
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
        
        # 绘制每艘船
        for idx, (_, ship) in enumerate(moment_data.iterrows()):
            color = colors[idx % len(colors)]
            
            # 绘制船舶领域
            if show_domain:
                self._draw_ship_domain(ax, ship['Lon'], ship['Lat'], ship['Cog'], 
                                     ship['Type'], ship['Length'], color, alpha=0.3)
            
            # 绘制船舶符号
            self._draw_ship_symbol(ax, ship['Lon'], ship['Lat'], ship['Cog'], 
                                 ship['Type'], color, size=max(50, ship['Length']))
            
            # 添加船舶信息标签
            if show_ship_info:
                info_text = f"MMSI: {int(ship['MMSI'])}\nL: {ship['Length']:.0f}m\nSOG: {ship['Sog']:.1f}kn"
                ax.annotate(info_text, (ship['Lon'], ship['Lat']), 
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=8, bbox=dict(boxstyle='round,pad=0.3', 
                           facecolor=color, alpha=0.7),
                           ha='left', va='bottom')
        
        # 设置标题
        ax.set_title(f'船舶位置和领域可视化\n时刻: {postime} ({len(moment_data)} 艘船)', 
                    fontsize=14, fontweight='bold', pad=20)
        
        # 添加图例
        if show_domain:
            ax.text(0.02, 0.98, '● 船舶符号\n○ 船舶领域', transform=ax.transAxes, 
                   fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图片
        output_file = os.path.join(output_dir, f"ship_domain_{postime}.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.show()
        plt.close()
        
        print(f"✅ 已保存: {output_file}")

    def visualize_time_series(self, trajectory_df, time_range=None, time_step=1,
                             output_dir='vis/ship_domain_time_series',
                             show_channel=True, show_map=True, show_domain=True,
                             show_ship_info=True, max_frames=50):
        """
        可视化时间序列的船舶位置和领域变化

        Args:
            trajectory_df: 轨迹数据DataFrame
            time_range: 时间范围 [start_time, end_time]，如果为None则使用全部时间
            time_step: 时间步长（秒）
            output_dir: 输出目录
            show_channel: 是否显示航道边界
            show_map: 是否显示底图
            show_domain: 是否显示船舶领域
            show_ship_info: 是否显示船舶信息
            max_frames: 最大帧数限制
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取时间范围
        if time_range is None:
            min_time = int(trajectory_df['PosTime'].min())
            max_time = int(trajectory_df['PosTime'].max())
        else:
            min_time, max_time = time_range

        # 生成时间序列
        times = list(range(min_time, max_time + 1, time_step))

        # 限制帧数
        if len(times) > max_frames:
            step = len(times) // max_frames
            times = times[::step]
            print(f"时间序列过长，已调整为 {len(times)} 帧")

        print(f"开始生成时间序列可视化，共 {len(times)} 帧...")

        # 计算全局显示范围（基于所有时刻的数据）
        time_filtered_df = trajectory_df[
            (trajectory_df['PosTime'] >= min_time) &
            (trajectory_df['PosTime'] <= max_time)
        ]
        global_extent = self._calculate_display_extent(time_filtered_df)
        figsize = self._calculate_figure_size(global_extent)

        # 生成每一帧
        for i, postime in enumerate(times):
            try:
                # 找到最接近的时刻数据
                available_times = trajectory_df['PosTime'].unique()
                closest_time = min(available_times, key=lambda x: abs(x - postime))

                moment_data = trajectory_df[trajectory_df['PosTime'] == closest_time].copy()

                if len(moment_data) == 0:
                    continue

                # 创建图形
                fig, ax = plt.subplots(1, 1, figsize=figsize)

                # 设置基础图形（使用全局范围）
                self._setup_base_plot(ax, global_extent, show_channel, show_map)

                # 定义颜色映射
                colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

                # 绘制每艘船
                for idx, (_, ship) in enumerate(moment_data.iterrows()):
                    color = colors[idx % len(colors)]

                    # 绘制船舶领域
                    if show_domain:
                        self._draw_ship_domain(ax, ship['Lon'], ship['Lat'], ship['Cog'],
                                             ship['Type'], ship['Length'], color, alpha=0.3)

                    # 绘制船舶符号
                    self._draw_ship_symbol(ax, ship['Lon'], ship['Lat'], ship['Cog'],
                                         ship['Type'], color, size=max(50, ship['Length']))

                    # 添加船舶信息标签
                    if show_ship_info:
                        info_text = f"MMSI: {int(ship['MMSI'])}\nL: {ship['Length']:.0f}m\nSOG: {ship['Sog']:.1f}kn"
                        ax.annotate(info_text, (ship['Lon'], ship['Lat']),
                                   xytext=(5, 5), textcoords='offset points',
                                   fontsize=8, bbox=dict(boxstyle='round,pad=0.3',
                                   facecolor=color, alpha=0.7),
                                   ha='left', va='bottom')

                # 设置标题
                ax.set_title(f'船舶位置和领域可视化 (帧 {i+1}/{len(times)})\n时刻: {closest_time} ({len(moment_data)} 艘船)',
                            fontsize=14, fontweight='bold', pad=20)

                # 添加图例
                if show_domain:
                    ax.text(0.02, 0.98, '● 船舶符号\n○ 船舶领域', transform=ax.transAxes,
                           fontsize=10, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

                plt.tight_layout()

                # 保存图片
                output_file = os.path.join(output_dir, f"frame_{i:04d}_{closest_time}.png")
                plt.savefig(output_file, dpi=300, bbox_inches='tight')
                plt.close()

                if (i + 1) % 10 == 0:
                    print(f"已完成 {i+1}/{len(times)} 帧")

            except Exception as e:
                print(f"生成第 {i+1} 帧失败: {e}")
                continue

        print(f"✅ 时间序列可视化完成，共生成 {len(times)} 帧")
        print(f"输出目录: {output_dir}")

    def visualize_conflict_moment(self, trajectory_df, conflicts_df, postime,
                                 output_dir='vis/ship_domain_conflict',
                                 show_channel=True, show_map=True,
                                 highlight_conflicts=True, conflict_threshold=0.1):
        """
        可视化冲突时刻的船舶位置和领域

        Args:
            trajectory_df: 轨迹数据DataFrame
            conflicts_df: 冲突数据DataFrame
            postime: 要可视化的时刻
            output_dir: 输出目录
            show_channel: 是否显示航道边界
            show_map: 是否显示底图
            highlight_conflicts: 是否高亮显示冲突船舶
            conflict_threshold: 冲突阈值
        """
        os.makedirs(output_dir, exist_ok=True)

        # 筛选指定时刻的数据
        moment_data = trajectory_df[trajectory_df['PosTime'] == postime].copy()
        moment_conflicts = conflicts_df[
            (conflicts_df['PosTime'] == postime) &
            (conflicts_df['conflict'] > conflict_threshold)
        ]

        if len(moment_data) == 0:
            print(f"时刻 {postime} 没有船舶数据")
            return

        # 计算显示范围和图片尺寸
        display_extent = self._calculate_display_extent(moment_data)
        figsize = self._calculate_figure_size(display_extent)

        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=figsize)

        # 设置基础图形
        self._setup_base_plot(ax, display_extent, show_channel, show_map)

        # 获取冲突船舶ID
        conflict_ship_ids = set()
        if highlight_conflicts and len(moment_conflicts) > 0:
            for _, conflict in moment_conflicts.iterrows():
                id1, id2 = conflict['id_pair']
                conflict_ship_ids.add(id1)
                conflict_ship_ids.add(id2)

        # 绘制每艘船
        for idx, (_, ship) in enumerate(moment_data.iterrows()):
            ship_id = ship['ID']

            # 根据是否冲突选择颜色
            if highlight_conflicts and ship_id in conflict_ship_ids:
                color = 'red'  # 冲突船舶用红色
                alpha = 0.5
                linewidth = 2
            else:
                color = 'blue'  # 非冲突船舶用蓝色
                alpha = 0.3
                linewidth = 1

            # 绘制船舶领域
            ellipse = self._draw_ship_domain(ax, ship['Lon'], ship['Lat'], ship['Cog'],
                                           ship['Type'], ship['Length'], color, alpha)
            ellipse.set_linewidth(linewidth)

            # 绘制船舶符号
            self._draw_ship_symbol(ax, ship['Lon'], ship['Lat'], ship['Cog'],
                                 ship['Type'], color, size=max(50, ship['Length']))

            # 添加船舶信息标签
            info_text = f"ID: {int(ship_id)}\nMMSI: {int(ship['MMSI'])}\nL: {ship['Length']:.0f}m"
            ax.annotate(info_text, (ship['Lon'], ship['Lat']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=8, bbox=dict(boxstyle='round,pad=0.3',
                       facecolor=color, alpha=0.7),
                       ha='left', va='bottom')

        # 绘制冲突连线
        if highlight_conflicts and len(moment_conflicts) > 0:
            for _, conflict in moment_conflicts.iterrows():
                id1, id2 = conflict['id_pair']
                ship1 = moment_data[moment_data['ID'] == id1]
                ship2 = moment_data[moment_data['ID'] == id2]

                if len(ship1) > 0 and len(ship2) > 0:
                    ship1 = ship1.iloc[0]
                    ship2 = ship2.iloc[0]

                    # 绘制冲突连线
                    ax.plot([ship1['Lon'], ship2['Lon']], [ship1['Lat'], ship2['Lat']],
                           'r--', linewidth=2, alpha=0.8, label=f'冲突值: {conflict["conflict"]:.3f}')

        # 设置标题
        conflict_count = len(moment_conflicts)
        ax.set_title(f'船舶冲突可视化\n时刻: {postime} ({len(moment_data)} 艘船, {conflict_count} 个冲突)',
                    fontsize=14, fontweight='bold', pad=20)

        # 添加图例
        legend_text = '● 船舶符号\n○ 船舶领域'
        if highlight_conflicts:
            legend_text += '\n🔴 冲突船舶\n🔵 正常船舶\n-- 冲突连线'

        ax.text(0.02, 0.98, legend_text, transform=ax.transAxes,
               fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()

        # 保存图片
        output_file = os.path.join(output_dir, f"conflict_{postime}_{conflict_count}conflicts.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.show()
        plt.close()

        print(f"✅ 已保存冲突可视化: {output_file}")
        print(f"冲突详情: {conflict_count} 个冲突")
        for _, conflict in moment_conflicts.iterrows():
            print(f"  - 船舶 {conflict['id_pair'][0]} 与 {conflict['id_pair'][1]}: 冲突值 {conflict['conflict']:.3f}")


def load_data():
    """加载数据"""
    try:
        # 加载轨迹数据
        trajectory_df = pd.read_parquet('data/2024_1_inter_note.parquet')
        print(f"✅ 加载轨迹数据: {len(trajectory_df)} 条记录")

        # 尝试加载冲突数据
        conflicts_df = None
        try:
            # 这里假设冲突数据已经计算并保存
            # 如果没有，可以运行51.complex_network.ipynb中的冲突计算部分
            conflicts_df = pd.read_parquet('data/conflicts_part.parquet')
            print(f"✅ 加载冲突数据: {len(conflicts_df)} 条记录")
        except:
            print("⚠️ 未找到冲突数据文件，将跳过冲突可视化功能")

        return trajectory_df, conflicts_df

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None


def main():
    """主函数"""
    print("🚢 船舶位置和领域真实可视化器")
    print("=" * 50)

    # 加载数据
    trajectory_df, conflicts_df = load_data()
    if trajectory_df is None:
        return

    # 创建可视化器
    visualizer = ShipDomainRealisticVisualizer()

    # ==================== 配置参数 ====================

    # 选择可视化模式
    mode = 'conflict'  # 'single', 'time_series', 'conflict'

    # 单时刻可视化参数
    single_postime = None  # 指定时刻，None表示随机选择

    # 时间序列可视化参数
    time_series_duration = 300  # 时间序列持续时间（秒）
    time_step = 20  # 时间步长（秒）
    max_frames = 30  # 最大帧数

    # 冲突可视化参数
    conflict_postime = None  # 指定冲突时刻，None表示自动选择
    conflict_threshold = 0.1  # 冲突阈值

    # 通用显示参数
    show_channel = True  # 显示航道边界
    show_map = True  # 显示底图
    show_domain = True  # 显示船舶领域
    show_ship_info = False # 显示船舶信息

    # ================================================

    if mode == 'single':
        # 单时刻可视化
        if single_postime is None:
            # 随机选择一个有较多船舶的时刻
            time_counts = trajectory_df.groupby('PosTime').size()
            top_times = time_counts.nlargest(10).index.tolist()
            single_postime = np.random.choice(top_times)

        print(f"📍 单时刻可视化 - 时刻: {single_postime}")
        visualizer.visualize_single_moment(
            trajectory_df, single_postime,
            show_channel=show_channel, show_map=show_map,
            show_domain=show_domain, show_ship_info=show_ship_info
        )

    elif mode == 'time_series':
        # 时间序列可视化
        print(f"🎬 时间序列可视化 - 持续时间: {time_series_duration}秒")

        # 随机选择时间段
        min_time = int(trajectory_df['PosTime'].min())
        max_time = int(trajectory_df['PosTime'].max())

        if max_time - min_time > time_series_duration:
            start_time = np.random.randint(min_time, max_time - time_series_duration)
            end_time = start_time + time_series_duration
        else:
            start_time, end_time = min_time, max_time

        print(f"时间范围: {start_time} - {end_time}")

        visualizer.visualize_time_series(
            trajectory_df, time_range=[start_time, end_time],
            time_step=time_step, max_frames=max_frames,
            show_channel=show_channel, show_map=show_map,
            show_domain=show_domain, show_ship_info=show_ship_info
        )

    elif mode == 'conflict':
        # 冲突可视化
        if conflicts_df is None:
            print("❌ 无冲突数据，无法进行冲突可视化")
            return

        if conflict_postime is None:
            # 选择冲突最多的时刻
            conflict_counts = conflicts_df[conflicts_df['conflict'] > conflict_threshold].groupby('PosTime').size()
            if len(conflict_counts) == 0:
                print("❌ 没有找到满足阈值的冲突数据")
                return
            conflict_postime = conflict_counts.idxmax()

        print(f"⚠️ 冲突可视化 - 时刻: {conflict_postime}")
        visualizer.visualize_conflict_moment(
            trajectory_df, conflicts_df, conflict_postime,
            show_channel=show_channel, show_map=show_map,
            conflict_threshold=conflict_threshold
        )

    else:
        print("❌ 无效的模式，请选择 'single', 'time_series' 或 'conflict'")

    print("\n✅ 可视化完成！")


def quick_visualize(mode='single', postime=None, show_domain=True, show_channel=True):
    """
    快速可视化函数

    Args:
        mode: 'single', 'time_series', 'conflict'
        postime: 指定时刻（仅对single和conflict模式有效）
        show_domain: 是否显示船舶领域
        show_channel: 是否显示航道边界
    """
    trajectory_df, conflicts_df = load_data()
    if trajectory_df is None:
        return

    visualizer = ShipDomainRealisticVisualizer()

    if mode == 'single':
        if postime is None:
            time_counts = trajectory_df.groupby('PosTime').size()
            postime = time_counts.idxmax()  # 选择船舶最多的时刻

        visualizer.visualize_single_moment(
            trajectory_df, postime, show_channel=show_channel,
            show_domain=show_domain, show_ship_info=True
        )

    elif mode == 'conflict' and conflicts_df is not None:
        if postime is None:
            conflict_counts = conflicts_df[conflicts_df['conflict'] > 0.1].groupby('PosTime').size()
            if len(conflict_counts) > 0:
                postime = conflict_counts.idxmax()
            else:
                print("没有找到冲突数据")
                return

        visualizer.visualize_conflict_moment(
            trajectory_df, conflicts_df, postime, show_channel=show_channel
        )

    else:
        print(f"模式 {mode} 不支持或缺少必要数据")


if __name__ == '__main__':
    main()
