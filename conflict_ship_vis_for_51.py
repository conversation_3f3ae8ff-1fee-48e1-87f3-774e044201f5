#!/usr/bin/env python3
"""
专门为51.complex_network.ipynb设计的船舶冲突可视化函数
可以直接在notebook中调用
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.patches import Ellipse, Polygon as MplPolygon
import pickle
import os

# 设置字体和样式
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'bold'


def draw_ship_symbol(ax, lon, lat, cog, color='red', size=50):
    """绘制船舶符号"""
    angle_rad = np.radians(90 - cog)
    ship_length = size * 1e-5
    ship_width = ship_length * 0.4
    
    bow_x = lon + ship_length * np.cos(angle_rad)
    bow_y = lat + ship_length * np.sin(angle_rad)
    
    stern_left_angle = angle_rad + np.pi * 2/3
    stern_left_x = lon + ship_width * np.cos(stern_left_angle)
    stern_left_y = lat + ship_width * np.sin(stern_left_angle)
    
    stern_right_angle = angle_rad - np.pi * 2/3
    stern_right_x = lon + ship_width * np.cos(stern_right_angle)
    stern_right_y = lat + ship_width * np.sin(stern_right_angle)
    
    triangle = MplPolygon([(bow_x, bow_y), (stern_left_x, stern_left_y), 
                          (stern_right_x, stern_right_y)], 
                         facecolor=color, edgecolor='black', linewidth=1, alpha=0.8)
    ax.add_patch(triangle)


def draw_ship_domain(ax, lon, lat, cog, ship_type, ship_length, color='red', alpha=0.3):
    """绘制船舶领域椭圆"""
    # 使用51.complex_network.ipynb中的vectorized_get_ab函数逻辑
    if ship_type == 1:  # 穿越船
        if ship_length <= 100:
            a_meters, b_meters = 271, 192
        else:
            a_meters, b_meters = 375, 210
    else:  # 非穿越船
        if ship_length <= 100:
            a_meters, b_meters = 180, 85
        else:
            a_meters, b_meters = 290, 120

    lat_rad = np.radians(lat)
    meters_per_degree_lon = 111320 * np.cos(lat_rad)
    meters_per_degree_lat = 111320

    a_degrees = a_meters / meters_per_degree_lon
    b_degrees = b_meters / meters_per_degree_lat
    angle = 90 - cog

    ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, 
                     angle=angle, facecolor=color, alpha=alpha, 
                     edgecolor=color, linewidth=1.5)
    ax.add_patch(ellipse)


def draw_channel_boundaries(ax):
    """绘制航道边界线"""
    try:
        with open('data/geo_info.pkl', 'rb') as f:
            geo_info = pickle.load(f)

        if 'channel_side1' in geo_info:
            side1_data = geo_info['channel_side1']
            if side1_data and len(side1_data) > 0:
                side1_lons = [p[0] for p in side1_data]
                side1_lats = [p[1] for p in side1_data]
                ax.plot(side1_lons, side1_lats, 'green', linewidth=2, alpha=0.8, linestyle='--')

        if 'channel_side2' in geo_info:
            side2_data = geo_info['channel_side2']
            if side2_data and len(side2_data) > 0:
                side2_lons = [p[0] for p in side2_data]
                side2_lats = [p[1] for p in side2_data]
                ax.plot(side2_lons, side2_lats, 'green', linewidth=2, alpha=0.8, linestyle='--')

        if 'channel_centerline' in geo_info:
            centerline_data = geo_info['channel_centerline']
            if centerline_data and len(centerline_data) > 0:
                center_lons = [p[0] for p in centerline_data]
                center_lats = [p[1] for p in centerline_data]
                ax.plot(center_lons, center_lats, 'blue', linewidth=1.5, alpha=0.6, linestyle='-')
    except Exception as e:
        pass


def visualize_conflict_ships(trajectory_note_df, conflicts_part_df, postime=None, 
                           conflict_threshold=0.01, show_map=True, show_channel=True):
    """
    可视化冲突时刻的船舶位置和领域
    
    Args:
        trajectory_note_df: 轨迹数据 (来自51.complex_network.ipynb)
        conflicts_part_df: 冲突数据 (来自51.complex_network.ipynb)
        postime: 指定时刻，None表示自动选择冲突最多的时刻
        conflict_threshold: 冲突阈值
        show_map: 是否显示底图
        show_channel: 是否显示航道边界
    """
    
    # 如果没有指定时刻，选择冲突最多的时刻
    if postime is None:
        conflict_data = conflicts_part_df[conflicts_part_df['conflict'] > conflict_threshold]
        if len(conflict_data) == 0:
            print("没有找到满足阈值的冲突数据")
            return
        
        conflict_counts = conflict_data.groupby('PosTime').size()
        postime = conflict_counts.idxmax()
        print(f"自动选择冲突最多的时刻: {postime} (共 {conflict_counts[postime]} 个冲突)")
    
    # 获取该时刻的冲突数据
    moment_conflicts = conflicts_part_df[
        (conflicts_part_df['PosTime'] == postime) & 
        (conflicts_part_df['conflict'] > conflict_threshold)
    ]
    
    if len(moment_conflicts) == 0:
        print(f"时刻 {postime} 没有冲突数据")
        return
    
    # 获取涉及冲突的船舶ID
    conflict_ship_ids = set()
    for _, conflict in moment_conflicts.iterrows():
        id1, id2 = conflict['id_pair']
        conflict_ship_ids.add(id1)
        conflict_ship_ids.add(id2)
    
    # 获取该时刻涉及冲突的船舶数据
    moment_ships = trajectory_note_df[
        (trajectory_note_df['PosTime'] == postime) & 
        (trajectory_note_df['ID'].isin(conflict_ship_ids))
    ].copy()
    
    if len(moment_ships) == 0:
        print(f"时刻 {postime} 没有找到对应的船舶数据")
        return
    
    # 计算显示范围
    lons = moment_ships['Lon'].values
    lats = moment_ships['Lat'].values
    
    lon_range = max(lons.max() - lons.min(), 0.01)
    lat_range = max(lats.max() - lats.min(), 0.01)
    
    expand_ratio = 0.3
    display_extent = [
        lons.min() - lon_range * expand_ratio, lons.max() + lon_range * expand_ratio,
        lats.min() - lat_range * expand_ratio, lats.max() + lat_range * expand_ratio
    ]
    
    # 计算图片尺寸
    center_lat = (display_extent[2] + display_extent[3]) / 2
    lat_km_per_degree = 111.0
    lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))
    
    actual_lon_distance = (display_extent[1] - display_extent[0]) * lon_km_per_degree
    actual_lat_distance = (display_extent[3] - display_extent[2]) * lat_km_per_degree
    geographic_aspect_ratio = actual_lat_distance / actual_lon_distance
    
    base_width = 12
    height = max(6, min(base_width * geographic_aspect_ratio, 20))
    figsize = (base_width, height)
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=figsize)
    
    # 设置底图
    if show_map:
        map_path = 'data/map0.png'
        base_map_extent = [121.050, 121.350, 31.516, 31.784]
        if os.path.exists(map_path):
            try:
                base_map = mpimg.imread(map_path)
                ax.imshow(base_map, extent=base_map_extent, aspect='auto', alpha=0.7)
            except Exception as e:
                pass
    
    # 设置显示范围
    ax.set_xlim(display_extent[0], display_extent[1])
    ax.set_ylim(display_extent[2], display_extent[3])
    
    # 绘制航道边界
    if show_channel:
        draw_channel_boundaries(ax)
    
    # 绘制船舶和领域
    for _, ship in moment_ships.iterrows():
        color = 'red'  # 所有冲突船舶都用红色
        
        # 绘制船舶领域
        draw_ship_domain(ax, ship['Lon'], ship['Lat'], ship['Cog'], 
                        ship['Type'], ship['Length'], color, alpha=0.4)
        
        # 绘制船舶符号
        draw_ship_symbol(ax, ship['Lon'], ship['Lat'], ship['Cog'], 
                        color, size=max(50, ship['Length']))
    
    # 绘制冲突连线
    for _, conflict in moment_conflicts.iterrows():
        id1, id2 = conflict['id_pair']
        ship1 = moment_ships[moment_ships['ID'] == id1]
        ship2 = moment_ships[moment_ships['ID'] == id2]
        
        if len(ship1) > 0 and len(ship2) > 0:
            ship1 = ship1.iloc[0]
            ship2 = ship2.iloc[0]
            
            ax.plot([ship1['Lon'], ship2['Lon']], [ship1['Lat'], ship2['Lat']], 
                   'r--', linewidth=2, alpha=0.8)
    
    # 设置标题和标签
    ax.set_title(f'船舶冲突可视化 - 时刻: {postime}\n{len(moment_ships)} 艘船, {len(moment_conflicts)} 个冲突', 
                fontsize=14, fontweight='bold', pad=20)
    ax.set_xlabel('经度', fontsize=12, fontweight='bold')
    ax.set_ylabel('纬度', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
    print(f"冲突详情:")
    for _, conflict in moment_conflicts.iterrows():
        print(f"  - 船舶 {conflict['id_pair'][0]} 与 {conflict['id_pair'][1]}: 冲突值 {conflict['conflict']:.3f}")


# 为了在51.complex_network.ipynb中方便使用，提供一个简化的调用函数
def show_conflict_ships(trajectory_note_df, conflicts_part_df, postime=None):
    """
    简化的冲突船舶可视化函数
    直接在51.complex_network.ipynb中调用
    """
    visualize_conflict_ships(trajectory_note_df, conflicts_part_df, postime, 
                           conflict_threshold=0.01, show_map=True, show_channel=True)


if __name__ == '__main__':
    print("这个模块专门为51.complex_network.ipynb设计")
    print("请在notebook中导入并使用:")
    print("from conflict_ship_vis_for_51 import show_conflict_ships")
    print("show_conflict_ships(trajectory_note_df, conflicts_part_df)")
